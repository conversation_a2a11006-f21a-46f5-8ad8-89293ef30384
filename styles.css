/* ===== CSS RESET AND BASE STYLES ===== */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

html {
    scroll-behavior: smooth;
    font-size: 16px;
}

body {
    font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, sans-serif;
    line-height: 1.7;
    color: var(--text-primary);
    background-color: var(--bg-primary);
    font-weight: 400;
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
    transition: background-color var(--transition-normal), color var(--transition-normal);
}

.container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 24px;
}

/* ===== DESIGN SYSTEM VARIABLES ===== */
:root {
    /* Colors */
    --primary-50: #f0f4ff;
    --primary-100: #e0e7ff;
    --primary-500: #6366f1;
    --primary-600: #4f46e5;
    --primary-700: #4338ca;
    --primary-900: #312e81;

    --secondary-500: #8b5cf6;
    --secondary-600: #7c3aed;

    --gray-50: #f9fafb;
    --gray-100: #f3f4f6;
    --gray-200: #e5e7eb;
    --gray-300: #d1d5db;
    --gray-400: #9ca3af;
    --gray-500: #6b7280;
    --gray-600: #4b5563;
    --gray-700: #374151;
    --gray-800: #1f2937;
    --gray-900: #111827;

    --success-500: #10b981;
    --error-500: #ef4444;

    /* Typography */
    --font-size-xs: 0.75rem;
    --font-size-sm: 0.875rem;
    --font-size-base: 1rem;
    --font-size-lg: 1.125rem;
    --font-size-xl: 1.25rem;
    --font-size-2xl: 1.5rem;
    --font-size-3xl: 1.875rem;
    --font-size-4xl: 2.25rem;
    --font-size-5xl: 3rem;
    --font-size-6xl: 3.75rem;

    /* Spacing */
    --space-1: 0.25rem;
    --space-2: 0.5rem;
    --space-3: 0.75rem;
    --space-4: 1rem;
    --space-5: 1.25rem;
    --space-6: 1.5rem;
    --space-8: 2rem;
    --space-10: 2.5rem;
    --space-12: 3rem;
    --space-16: 4rem;
    --space-20: 5rem;
    --space-24: 6rem;

    /* Shadows */
    --shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
    --shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
    --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
    --shadow-xl: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
    --shadow-2xl: 0 25px 50px -12px rgba(0, 0, 0, 0.25);

    /* Border radius */
    --radius-sm: 0.375rem;
    --radius-md: 0.5rem;
    --radius-lg: 0.75rem;
    --radius-xl: 1rem;
    --radius-2xl: 1.5rem;

    /* Transitions */
    --transition-fast: 150ms ease-in-out;
    --transition-normal: 250ms ease-in-out;
    --transition-slow: 350ms ease-in-out;

    /* Theme Variables - Light Mode (Default) */
    --bg-primary: #ffffff;
    --bg-secondary: var(--gray-50);
    --bg-tertiary: var(--gray-100);
    --text-primary: var(--gray-900);
    --text-secondary: var(--gray-600);
    --text-tertiary: var(--gray-500);
    --border-color: var(--gray-200);
    --shadow-color: rgba(0, 0, 0, 0.1);
    --nav-bg: rgba(255, 255, 255, 0.95);
    --nav-bg-scrolled: rgba(255, 255, 255, 0.98);

    /* Text on colored backgrounds (always white for readability) */
    --text-on-primary: #ffffff;
    --text-on-primary-muted: rgba(255, 255, 255, 0.9);
    --text-on-primary-subtle: rgba(255, 255, 255, 0.8);

    /* Form message colors */
    --success-bg: linear-gradient(135deg, #d1fae5 0%, #a7f3d0 100%);
    --success-text: #065f46;
    --success-border: #10b981;
    --error-bg: linear-gradient(135deg, #fee2e2 0%, #fecaca 100%);
    --error-text: #991b1b;
    --error-border: #ef4444;
}

/* Dark Theme */
[data-theme="dark"] {
    --primary-50: #1e1b4b;
    --primary-100: #312e81;
    --primary-500: #6366f1;
    --primary-600: #818cf8;
    --primary-700: #a5b4fc;
    --primary-900: #e0e7ff;

    --secondary-500: #c084fc;
    --secondary-600: #d8b4fe;

    --gray-50: #0f172a;
    --gray-100: #1e293b;
    --gray-200: #334155;
    --gray-300: #475569;
    --gray-400: #64748b;
    --gray-500: #94a3b8;
    --gray-600: #cbd5e1;
    --gray-700: #e2e8f0;
    --gray-800: #f1f5f9;
    --gray-900: #f8fafc;

    /* Dark Theme Variables */
    --bg-primary: var(--gray-50);
    --bg-secondary: var(--gray-100);
    --bg-tertiary: var(--gray-200);
    --text-primary: var(--gray-900);
    --text-secondary: var(--gray-600);
    --text-tertiary: var(--gray-500);
    --border-color: var(--gray-300);
    --shadow-color: rgba(0, 0, 0, 0.3);
    --nav-bg: rgba(15, 23, 42, 0.95);
    --nav-bg-scrolled: rgba(15, 23, 42, 0.98);

    /* Text on colored backgrounds (always white for readability) */
    --text-on-primary: #ffffff;
    --text-on-primary-muted: rgba(255, 255, 255, 0.9);
    --text-on-primary-subtle: rgba(255, 255, 255, 0.8);

    /* Form message colors - adjusted for dark theme */
    --success-bg: linear-gradient(135deg, #064e3b 0%, #065f46 100%);
    --success-text: #a7f3d0;
    --success-border: #10b981;
    --error-bg: linear-gradient(135deg, #7f1d1d 0%, #991b1b 100%);
    --error-text: #fecaca;
    --error-border: #ef4444;
}

/* ===== ENHANCED TYPOGRAPHY ===== */
h1, h2, h3, h4, h5, h6 {
    font-weight: 700;
    margin-bottom: var(--space-4);
    letter-spacing: -0.025em;
    color: var(--text-primary);
}

h1 {
    font-size: var(--font-size-5xl);
    line-height: 1.1;
    font-weight: 800;
}

h2 {
    font-size: var(--font-size-4xl);
    line-height: 1.2;
}

h3 {
    font-size: var(--font-size-3xl);
    line-height: 1.3;
}

h4 {
    font-size: var(--font-size-2xl);
    line-height: 1.4;
}

h5 {
    font-size: var(--font-size-xl);
    line-height: 1.5;
}

h6 {
    font-size: var(--font-size-lg);
    line-height: 1.5;
}

p {
    margin-bottom: var(--space-4);
    color: var(--text-secondary);
    font-size: var(--font-size-lg);
    line-height: 1.7;
}

.text-large {
    font-size: var(--font-size-xl);
    line-height: 1.6;
}

.text-small {
    font-size: var(--font-size-sm);
    line-height: 1.5;
}

.text-center {
    text-align: center;
}

.text-primary {
    color: var(--primary-600);
}

.text-secondary {
    color: var(--secondary-600);
}

.font-medium {
    font-weight: 500;
}

.font-semibold {
    font-weight: 600;
}

.font-bold {
    font-weight: 700;
}

/* ===== ENHANCED BUTTONS ===== */
.btn {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    gap: var(--space-2);
    padding: var(--space-3) var(--space-8);
    text-decoration: none;
    border-radius: var(--radius-lg);
    font-weight: 600;
    font-size: var(--font-size-base);
    text-align: center;
    transition: all var(--transition-normal);
    border: none;
    cursor: pointer;
    position: relative;
    overflow: hidden;
    white-space: nowrap;
}

.btn::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
    transition: left var(--transition-slow);
}

.btn:hover::before {
    left: 100%;
}

.btn-primary {
    background: linear-gradient(135deg, var(--primary-600) 0%, var(--secondary-600) 100%);
    color: var(--text-on-primary);
    box-shadow: var(--shadow-md);
    font-weight: 600;
}

.btn-primary:hover {
    transform: translateY(-2px);
    box-shadow: 0 20px 40px rgba(99, 102, 241, 0.3);
    background: linear-gradient(135deg, var(--primary-500) 0%, var(--secondary-500) 100%);
    color: var(--text-on-primary);
}

.btn-primary:active {
    transform: translateY(0);
    box-shadow: var(--shadow-lg);
}

.btn-secondary {
    background: var(--bg-primary);
    color: var(--primary-600);
    border: 2px solid var(--primary-600);
    box-shadow: var(--shadow-sm);
    font-weight: 600;
}

.btn-secondary:hover {
    background: var(--primary-600);
    color: var(--text-on-primary);
    transform: translateY(-1px);
    box-shadow: var(--shadow-lg);
}

.btn-large {
    padding: var(--space-4) var(--space-10);
    font-size: var(--font-size-lg);
}

.btn-small {
    padding: var(--space-2) var(--space-6);
    font-size: var(--font-size-sm);
}

/* ===== ENHANCED HEADER AND NAVIGATION ===== */
.header {
    background: var(--nav-bg);
    backdrop-filter: blur(20px);
    -webkit-backdrop-filter: blur(20px);
    position: fixed;
    top: 0;
    width: 100%;
    z-index: 1000;
    box-shadow: var(--shadow-sm);
    border-bottom: 1px solid var(--border-color);
    transition: all var(--transition-normal);
}

.header.scrolled {
    background: var(--nav-bg-scrolled);
    box-shadow: var(--shadow-lg);
}

.navbar {
    padding: var(--space-4) 0;
}

.nav-container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 var(--space-6);
    display: grid;
    grid-template-columns: auto 1fr auto;
    align-items: center;
    gap: var(--space-6);
}

.nav-logo {
    display: flex;
    align-items: center;
    gap: var(--space-3);
    justify-self: start;
}

.logo-image {
    height: 40px;
    width: auto;
    transition: transform var(--transition-normal);
}

.logo-image:hover {
    transform: scale(1.05);
}

.nav-logo h2 {
    background: linear-gradient(135deg, var(--primary-600) 0%, var(--secondary-600) 100%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    margin: 0;
    font-size: var(--font-size-2xl);
    font-weight: 800;
    letter-spacing: -0.02em;
}

.nav-menu {
    display: flex;
    list-style: none;
    gap: var(--space-8);
    align-items: center;
    justify-self: center;
    margin: 0;
    padding: 0;
}

.nav-link {
    text-decoration: none;
    color: var(--text-secondary);
    font-weight: 500;
    font-size: var(--font-size-base);
    transition: all var(--transition-normal);
    position: relative;
    padding: var(--space-2) 0;
}

.nav-link::after {
    content: '';
    position: absolute;
    bottom: 0;
    left: 0;
    width: 0;
    height: 2px;
    background: linear-gradient(135deg, var(--primary-600) 0%, var(--secondary-600) 100%);
    transition: width var(--transition-normal);
}

.nav-link:hover,
.nav-link.active {
    color: var(--primary-600);
}

.nav-link:hover::after,
.nav-link.active::after {
    width: 100%;
}

/* Theme Toggle */
.theme-toggle {
    justify-self: end;
}

.theme-toggle-btn {
    background: var(--bg-tertiary);
    border: 1px solid var(--border-color);
    border-radius: var(--radius-lg);
    padding: var(--space-2);
    cursor: pointer;
    transition: all var(--transition-normal);
    display: flex;
    align-items: center;
    justify-content: center;
    width: 40px;
    height: 40px;
}

.theme-toggle-btn:hover {
    background: var(--primary-50);
    border-color: var(--primary-200);
    transform: scale(1.05);
}

.theme-icon {
    font-size: 18px;
    transition: transform var(--transition-normal);
}

.theme-toggle-btn:hover .theme-icon {
    transform: rotate(20deg);
}

.hamburger {
    display: none;
    flex-direction: column;
    cursor: pointer;
    padding: var(--space-2);
    border-radius: var(--radius-md);
    transition: background-color var(--transition-fast);
}

.hamburger:hover {
    background-color: var(--gray-100);
}

.bar {
    width: 24px;
    height: 2px;
    background: var(--gray-700);
    margin: 2px 0;
    transition: all var(--transition-normal);
    border-radius: 2px;
}

/* ===== ENHANCED HERO SECTION ===== */
.hero {
    background: linear-gradient(135deg, var(--primary-600) 0%, var(--secondary-600) 100%);
    color: var(--text-on-primary);
    padding: calc(var(--space-24) + 60px) 0 var(--space-24);
    min-height: 100vh;
    display: flex;
    align-items: center;
    position: relative;
    overflow: hidden;
}

.hero::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 1000 1000"><defs><radialGradient id="a" cx="50%" cy="50%"><stop offset="0%" stop-color="rgba(255,255,255,0.1)"/><stop offset="100%" stop-color="rgba(255,255,255,0)"/></radialGradient></defs><circle cx="200" cy="200" r="100" fill="url(%23a)"/><circle cx="800" cy="300" r="150" fill="url(%23a)"/><circle cx="400" cy="700" r="80" fill="url(%23a)"/></svg>') no-repeat;
    background-size: cover;
    opacity: 0.3;
}

.hero-container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 var(--space-6);
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: var(--space-16);
    align-items: center;
    position: relative;
    z-index: 1;
}

.hero-content h1 {
    font-size: var(--font-size-3xl);
    margin-bottom: var(--space-6);
    line-height: 1.1;
    font-weight: 800;
    letter-spacing: -0.02em;
}

.hero-content .hero-subtitle {
    font-size: var(--font-size-2xl);
    margin-bottom: var(--space-8);
    color: var(--text-on-primary-muted);
    line-height: 1.6;
    font-weight: 400;
}

.hero-buttons {
    display: flex;
    gap: var(--space-4);
    flex-wrap: wrap;
    margin-bottom: var(--space-8);
}

.hero-stats {
    display: flex;
    gap: var(--space-8);
    margin-top: var(--space-8);
}

.hero-stat {
    text-align: center;
}

.hero-stat-number {
    display: block;
    font-size: var(--font-size-3xl);
    font-weight: 800;
    color: var(--text-on-primary);
    line-height: 1;
}

.hero-stat-label {
    font-size: var(--font-size-sm);
    color: var(--text-on-primary-subtle);
    margin-top: var(--space-1);
}

.hero-image {
    display: flex;
    justify-content: center;
    align-items: center;
    position: relative;
}

.hero-graphic {
    width: 400px;
    height: 400px;
    position: relative;
}

.graphic-element {
    position: absolute;
    border-radius: 50%;
    background: rgba(255, 255, 255, 0.1);
    backdrop-filter: blur(20px);
    -webkit-backdrop-filter: blur(20px);
    border: 1px solid rgba(255, 255, 255, 0.2);
}

.graphic-element:nth-child(1) {
    width: 250px;
    height: 250px;
    top: 75px;
    left: 75px;
    animation: float 8s ease-in-out infinite;
    background: linear-gradient(135deg, rgba(255, 255, 255, 0.15) 0%, rgba(255, 255, 255, 0.05) 100%);
}

.graphic-element:nth-child(2) {
    width: 120px;
    height: 120px;
    top: 40px;
    left: 40px;
    animation: float 6s ease-in-out infinite reverse;
    background: linear-gradient(135deg, rgba(255, 255, 255, 0.2) 0%, rgba(255, 255, 255, 0.1) 100%);
}

.graphic-element:nth-child(3) {
    width: 80px;
    height: 80px;
    bottom: 40px;
    right: 40px;
    animation: float 7s ease-in-out infinite;
    background: linear-gradient(135deg, rgba(255, 255, 255, 0.25) 0%, rgba(255, 255, 255, 0.15) 100%);
}

.graphic-element:nth-child(4) {
    width: 60px;
    height: 60px;
    top: 120px;
    right: 60px;
    animation: float 5s ease-in-out infinite reverse;
    background: linear-gradient(135deg, rgba(255, 255, 255, 0.3) 0%, rgba(255, 255, 255, 0.2) 100%);
}

.hero-logo {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    z-index: 15;
    animation: float 8s ease-in-out infinite;
    background: rgba(255, 255, 255, 0.15);
    backdrop-filter: blur(25px);
    -webkit-backdrop-filter: blur(25px);
    border: 3px solid rgba(255, 255, 255, 0.3);
    border-radius: 50%;
    padding: var(--space-6);
    display: flex;
    align-items: center;
    justify-content: center;
    width: 140px;
    height: 140px;
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
}

.hero-logo-image {
    width: 70px;
    height: 70px;
    object-fit: contain;
    filter: brightness(0) invert(1);
    transition: all var(--transition-normal);
}

.hero-logo:hover {
    transform: translate(-50%, -50%) scale(1.05);
    background: rgba(255, 255, 255, 0.15);
}

.hero-logo:hover .hero-logo-image {
    transform: scale(1.1);
}

@keyframes float {
    0%, 100% {
        transform: translateY(0px) rotate(0deg);
    }
    50% {
        transform: translateY(-30px) rotate(180deg);
    }
}

/* ===== ENHANCED SECTION STYLES ===== */
section {
    padding: var(--space-24) 0;
    position: relative;
}

.section-header {
    text-align: center;
    margin-bottom: var(--space-16);
    max-width: 800px;
    margin-left: auto;
    margin-right: auto;
}

.section-header h2 {
    color: var(--text-primary);
    margin-bottom: var(--space-4);
    position: relative;
}

.section-header h2::after {
    content: '';
    position: absolute;
    bottom: -var(--space-2);
    left: 50%;
    transform: translateX(-50%);
    width: 60px;
    height: 4px;
    background: linear-gradient(135deg, var(--primary-600) 0%, var(--secondary-600) 100%);
    border-radius: 2px;
}

.section-header p {
    font-size: var(--font-size-xl);
    color: var(--text-secondary);
    max-width: 600px;
    margin: 0 auto;
    line-height: 1.7;
}

.section-header .section-badge {
    display: inline-block;
    background: linear-gradient(135deg, var(--primary-50) 0%, var(--secondary-50) 100%);
    color: var(--primary-700);
    padding: var(--space-2) var(--space-4);
    border-radius: var(--radius-xl);
    font-size: var(--font-size-sm);
    font-weight: 600;
    margin-bottom: var(--space-4);
    border: 1px solid var(--primary-200);
}

/* ===== ENHANCED ABOUT SECTION ===== */
.about {
    background: var(--bg-secondary);
    position: relative;
}

.about::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grid" width="10" height="10" patternUnits="userSpaceOnUse"><path d="M 10 0 L 0 0 0 10" fill="none" stroke="rgba(99,102,241,0.05)" stroke-width="1"/></pattern></defs><rect width="100" height="100" fill="url(%23grid)"/></svg>');
    opacity: 0.5;
}

.about-content {
    display: grid;
    grid-template-columns: 2fr 1fr;
    gap: var(--space-16);
    align-items: start;
    position: relative;
    z-index: 1;
}

.about-text {
    background: var(--card-bg);
    padding: var(--space-10);
    border-radius: var(--radius-2xl);
    box-shadow: var(--shadow-lg);
    border: 1px solid var(--border-color);
}

.about-text h3 {
    color: var(--gray-900);
    margin-bottom: var(--space-4);
    margin-top: var(--space-8);
    display: flex;
    align-items: center;
    gap: var(--space-3);
}

.about-text h3:first-child {
    margin-top: 0;
}

.about-text h3::before {
    content: '';
    width: 4px;
    height: 24px;
    background: linear-gradient(135deg, var(--primary-600) 0%, var(--secondary-600) 100%);
    border-radius: 2px;
}

.about-text p {
    color: var(--gray-600);
    line-height: 1.8;
}

.about-stats {
    display: flex;
    flex-direction: column;
    gap: var(--space-6);
}

.stat {
    text-align: center;
    padding: var(--space-8);
    background: var(--card-bg);
    border-radius: var(--radius-xl);
    box-shadow: var(--shadow-lg);
    border: 1px solid var(--border-color);
    transition: all var(--transition-normal);
    position: relative;
    overflow: hidden;
}

.stat::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
    background: linear-gradient(135deg, var(--primary-600) 0%, var(--secondary-600) 100%);
}

.stat:hover {
    transform: translateY(-4px);
    box-shadow: var(--shadow-xl);
}

.stat h4 {
    font-size: var(--font-size-4xl);
    background: linear-gradient(135deg, var(--primary-600) 0%, var(--secondary-600) 100%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    margin-bottom: var(--space-2);
    font-weight: 800;
}

.stat p {
    color: var(--gray-600);
    font-weight: 600;
    font-size: var(--font-size-base);
    margin: 0;
}

/* ===== ENHANCED SERVICES SECTION ===== */
.services {
    background: var(--bg-primary);
    position: relative;
}

.services-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(320px, 1fr));
    gap: var(--space-8);
}

.service-card {
    background: var(--card-bg);
    padding: var(--space-10);
    border-radius: var(--radius-2xl);
    box-shadow: var(--shadow-lg);
    border: 1px solid var(--border-color);
    transition: all var(--transition-normal);
    text-align: center;
    position: relative;
    overflow: hidden;
}

.service-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 6px;
    background: linear-gradient(135deg, var(--primary-600) 0%, var(--secondary-600) 100%);
    transform: scaleX(0);
    transition: transform var(--transition-normal);
}

.service-card:hover::before {
    transform: scaleX(1);
}

.service-card:hover {
    transform: translateY(-8px);
    box-shadow: var(--shadow-2xl);
    border-color: var(--primary-200);
}

.service-icon {
    width: 80px;
    height: 80px;
    margin: 0 auto var(--space-6);
    background: linear-gradient(135deg, var(--primary-100) 0%, var(--secondary-100) 100%);
    border-radius: var(--radius-xl);
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: var(--font-size-4xl);
    transition: all var(--transition-normal);
    border: 2px solid var(--primary-200);
}

.service-card:hover .service-icon {
    background: linear-gradient(135deg, var(--primary-600) 0%, var(--secondary-600) 100%);
    transform: scale(1.1) rotate(5deg);
    border-color: var(--primary-600);
}

.service-card h3 {
    color: var(--gray-900);
    margin-bottom: var(--space-4);
    font-size: var(--font-size-2xl);
}

.service-card > p {
    margin-bottom: var(--space-6);
    color: var(--gray-600);
    line-height: 1.7;
}

.service-features {
    background: var(--gray-50);
    border-radius: var(--radius-lg);
    padding: var(--space-6);
    margin-top: var(--space-6);
}

.service-card ul {
    list-style: none;
    text-align: left;
    margin: 0;
}

.service-card li {
    padding: var(--space-3) 0;
    color: var(--gray-700);
    position: relative;
    padding-left: var(--space-8);
    font-size: var(--font-size-base);
    font-weight: 500;
    transition: color var(--transition-fast);
}

.service-card li:hover {
    color: var(--primary-600);
}

.service-card li::before {
    content: "✓";
    position: absolute;
    left: 0;
    top: var(--space-3);
    color: var(--primary-600);
    font-weight: 700;
    font-size: var(--font-size-lg);
    width: 20px;
    height: 20px;
    background: var(--primary-100);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: var(--font-size-sm);
}

.service-card:nth-child(1) .service-icon {
    background: linear-gradient(135deg, #dbeafe 0%, #e0e7ff 100%);
}

.service-card:nth-child(2) .service-icon {
    background: linear-gradient(135deg, #fef3c7 0%, #fde68a 100%);
}

.service-card:nth-child(3) .service-icon {
    background: linear-gradient(135deg, #d1fae5 0%, #a7f3d0 100%);
}

.service-card:nth-child(4) .service-icon {
    background: linear-gradient(135deg, #fce7f3 0%, #fbcfe8 100%);
}

/* ===== ENHANCED EXPERIENCE TIMELINE ===== */
.experience {
    background: linear-gradient(135deg, var(--primary-50) 0%, var(--gray-50) 100%);
    position: relative;
}

.experience::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="dots" width="20" height="20" patternUnits="userSpaceOnUse"><circle cx="10" cy="10" r="1" fill="rgba(99,102,241,0.1)"/></pattern></defs><rect width="100" height="100" fill="url(%23dots)"/></svg>');
    opacity: 0.6;
}

.timeline {
    position: relative;
    max-width: 900px;
    margin: 0 auto;
    z-index: 1;
}

.timeline::before {
    content: '';
    position: absolute;
    left: 50%;
    top: 0;
    bottom: 0;
    width: 4px;
    background: linear-gradient(180deg, var(--primary-600) 0%, var(--secondary-600) 100%);
    transform: translateX(-50%);
    border-radius: 2px;
    box-shadow: var(--shadow-sm);
}

.timeline-item {
    display: flex;
    margin-bottom: var(--space-12);
    position: relative;
}

.timeline-item:nth-child(odd) {
    flex-direction: row;
}

.timeline-item:nth-child(even) {
    flex-direction: row-reverse;
}

.timeline-year {
    background: linear-gradient(135deg, var(--primary-600) 0%, var(--secondary-600) 100%);
    color: var(--text-on-primary);
    padding: var(--space-4) var(--space-6);
    border-radius: var(--radius-xl);
    font-weight: 700;
    font-size: var(--font-size-xl);
    position: absolute;
    left: 50%;
    transform: translateX(-50%);
    z-index: 3;
    box-shadow: var(--shadow-lg);
    border: 4px solid white;
    min-width: 100px;
    text-align: center;
}

.timeline-content {
    background: var(--card-bg);
    padding: var(--space-8);
    border-radius: var(--radius-xl);
    box-shadow: var(--shadow-lg);
    border: 1px solid var(--border-color);
    width: 45%;
    margin-top: var(--space-12);
    position: relative;
    transition: all var(--transition-normal);
}

.timeline-content:hover {
    transform: translateY(-4px);
    box-shadow: var(--shadow-xl);
}

.timeline-content::before {
    content: '';
    position: absolute;
    top: var(--space-8);
    width: 0;
    height: 0;
    border: 12px solid transparent;
}

.timeline-item:nth-child(odd) .timeline-content {
    margin-right: auto;
}

.timeline-item:nth-child(odd) .timeline-content::before {
    right: -24px;
    border-left-color: var(--bg-primary);
}

.timeline-item:nth-child(even) .timeline-content {
    margin-left: auto;
}

.timeline-item:nth-child(even) .timeline-content::before {
    left: -24px;
    border-right-color: var(--bg-primary);
}

.timeline-content h3 {
    color: var(--gray-900);
    margin-bottom: var(--space-4);
    font-size: var(--font-size-2xl);
}

.timeline-content p {
    color: var(--gray-600);
    line-height: 1.7;
    margin: 0;
}

/* ===== ENHANCED SUCCESS STORIES ===== */
.success {
    background: var(--bg-color);
    position: relative;
}

.testimonials-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(380px, 1fr));
    gap: var(--space-8);
}

.testimonial-card {
    background: var(--card-bg);
    padding: var(--space-10);
    border-radius: var(--radius-2xl);
    box-shadow: var(--shadow-lg);
    border: 1px solid var(--border-color);
    transition: all var(--transition-normal);
    position: relative;
    overflow: hidden;
}

.testimonial-card::before {
    content: '"';
    position: absolute;
    top: var(--space-6);
    right: var(--space-6);
    font-size: 4rem;
    color: var(--primary-100);
    font-family: Georgia, serif;
    line-height: 1;
}

.testimonial-card:hover {
    transform: translateY(-6px);
    box-shadow: var(--shadow-2xl);
    border-color: var(--primary-200);
}

.testimonial-content {
    position: relative;
    z-index: 1;
}

.testimonial-content p {
    font-style: italic;
    font-size: var(--font-size-lg);
    margin-bottom: var(--space-8);
    color: var(--gray-700);
    line-height: 1.8;
    position: relative;
}

.testimonial-author {
    display: flex;
    align-items: center;
    margin-bottom: var(--space-6);
    gap: var(--space-4);
}

.author-avatar {
    width: 60px;
    height: 60px;
    border-radius: 50%;
    background: linear-gradient(135deg, var(--primary-600) 0%, var(--secondary-600) 100%);
    display: flex;
    align-items: center;
    justify-content: center;
    color: var(--text-on-primary);
    font-weight: 700;
    font-size: var(--font-size-xl);
    flex-shrink: 0;
}

.author-info h4 {
    color: var(--gray-900);
    margin-bottom: var(--space-1);
    font-size: var(--font-size-lg);
}

.author-info span {
    color: var(--primary-600);
    font-size: var(--font-size-base);
    font-weight: 500;
}

.testimonial-results {
    display: flex;
    gap: var(--space-8);
    border-top: 2px solid var(--gray-100);
    padding-top: var(--space-6);
    justify-content: space-around;
}

.result {
    text-align: center;
    flex: 1;
}

.result-number {
    display: block;
    font-size: var(--font-size-3xl);
    font-weight: 800;
    background: linear-gradient(135deg, var(--primary-600) 0%, var(--secondary-600) 100%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    line-height: 1.2;
}

.result-label {
    font-size: var(--font-size-sm);
    color: var(--gray-600);
    font-weight: 600;
    margin-top: var(--space-1);
}

.testimonial-card:nth-child(1) {
    border-top: 4px solid var(--primary-500);
}

.testimonial-card:nth-child(2) {
    border-top: 4px solid var(--secondary-500);
}

.testimonial-card:nth-child(3) {
    border-top: 4px solid var(--primary-600);
}

/* ===== ENHANCED VALUE PROPOSITION ===== */
.value {
    background: linear-gradient(135deg, var(--gray-50) 0%, var(--primary-50) 100%);
    position: relative;
}

.value::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="hexagons" width="28" height="24" patternUnits="userSpaceOnUse"><polygon points="14,2 26,8 26,20 14,26 2,20 2,8" fill="none" stroke="rgba(99,102,241,0.05)" stroke-width="1"/></pattern></defs><rect width="100" height="100" fill="url(%23hexagons)"/></svg>');
    opacity: 0.7;
}

.value-content {
    display: grid;
    grid-template-columns: 2fr 1fr;
    gap: var(--space-16);
    align-items: start;
    position: relative;
    z-index: 1;
}

.value-main {
    background: var(--card-bg);
    padding: var(--space-10);
    border-radius: var(--radius-2xl);
    box-shadow: var(--shadow-lg);
    border: 1px solid var(--border-color);
}

.value-main h3 {
    color: var(--gray-900);
    margin-bottom: var(--space-6);
    font-size: var(--font-size-3xl);
}

.value-main > p {
    color: var(--gray-600);
    font-size: var(--font-size-lg);
    line-height: 1.8;
    margin-bottom: var(--space-8);
}

.value-benefits {
    margin-top: var(--space-8);
}

.benefit {
    display: flex;
    gap: var(--space-6);
    margin-bottom: var(--space-8);
    align-items: flex-start;
    padding: var(--space-6);
    background: var(--gray-50);
    border-radius: var(--radius-xl);
    border-left: 4px solid var(--primary-500);
    transition: all var(--transition-normal);
}

.benefit:hover {
    background: var(--primary-50);
    transform: translateX(4px);
}

.benefit-icon {
    font-size: var(--font-size-3xl);
    flex-shrink: 0;
    width: 60px;
    height: 60px;
    background: linear-gradient(135deg, var(--primary-100) 0%, var(--secondary-100) 100%);
    border-radius: var(--radius-lg);
    display: flex;
    align-items: center;
    justify-content: center;
    border: 2px solid var(--primary-200);
}

.benefit-content h4 {
    color: var(--gray-900);
    margin-bottom: var(--space-2);
    font-size: var(--font-size-xl);
}

.benefit-content p {
    color: var(--gray-600);
    line-height: 1.7;
    margin: 0;
}

.cta-box {
    background: linear-gradient(135deg, var(--primary-600) 0%, var(--secondary-600) 100%);
    color: var(--text-on-primary);
    padding: var(--space-10);
    border-radius: var(--radius-2xl);
    text-align: center;
    position: sticky;
    top: 120px;
    box-shadow: var(--shadow-xl);
    border: 1px solid var(--primary-700);
}

.cta-box h3 {
    margin-bottom: var(--space-4);
    font-size: var(--font-size-2xl);
}

.cta-box p {
    color: var(--text-on-primary-muted);
    margin-bottom: var(--space-8);
    font-size: var(--font-size-lg);
    line-height: 1.6;
}

.cta-guarantee {
    margin-top: var(--space-6);
    padding: var(--space-4);
    background: rgba(255, 255, 255, 0.1);
    border-radius: var(--radius-lg);
    border: 1px solid rgba(255, 255, 255, 0.2);
}

.cta-guarantee small {
    color: var(--text-on-primary-muted);
    font-size: var(--font-size-sm);
    font-weight: 500;
}

/* ===== ENHANCED CONTACT SECTION ===== */
.contact {
    background: var(--bg-primary);
    position: relative;
}

.contact::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(135deg, transparent 0%, var(--primary-50) 100%);
    opacity: 0.5;
}

.contact-content {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: var(--space-16);
    position: relative;
    z-index: 1;
}

.contact-info {
    background: var(--card-bg);
    padding: var(--space-10);
    border-radius: var(--radius-2xl);
    box-shadow: var(--shadow-lg);
    border: 1px solid var(--border-color);
    height: fit-content;
}

.contact-info h3 {
    color: var(--gray-900);
    margin-bottom: var(--space-6);
    font-size: var(--font-size-3xl);
}

.contact-info > p {
    color: var(--gray-600);
    font-size: var(--font-size-lg);
    line-height: 1.7;
    margin-bottom: var(--space-8);
}

.contact-details {
    margin-top: var(--space-8);
}

.contact-item {
    display: flex;
    gap: var(--space-4);
    margin-bottom: var(--space-8);
    align-items: flex-start;
    padding: var(--space-6);
    background: var(--gray-50);
    border-radius: var(--radius-lg);
    transition: all var(--transition-normal);
    border-left: 4px solid transparent;
}

.contact-item:hover {
    background: var(--primary-50);
    border-left-color: var(--primary-500);
    transform: translateX(4px);
}

.contact-icon {
    font-size: var(--font-size-2xl);
    flex-shrink: 0;
    width: 50px;
    height: 50px;
    background: linear-gradient(135deg, var(--primary-100) 0%, var(--secondary-100) 100%);
    border-radius: var(--radius-lg);
    display: flex;
    align-items: center;
    justify-content: center;
    border: 2px solid var(--primary-200);
}

.contact-item h4 {
    color: var(--gray-900);
    margin-bottom: var(--space-2);
    font-size: var(--font-size-lg);
}

.contact-item p {
    color: var(--gray-600);
    margin: 0;
    line-height: 1.6;
}

/* ===== ENHANCED CONTACT FORM ===== */
.contact-form-container {
    background: var(--card-bg);
    border-radius: var(--radius-2xl);
    box-shadow: var(--shadow-lg);
    border: 1px solid var(--border-color);
    overflow: hidden;
}

.contact-form {
    padding: var(--space-10);
}

.contact-form h3 {
    color: var(--gray-900);
    margin-bottom: var(--space-8);
    text-align: center;
    font-size: var(--font-size-3xl);
}

.form-group {
    margin-bottom: var(--space-6);
}

.form-group label {
    display: block;
    margin-bottom: var(--space-2);
    color: var(--gray-700);
    font-weight: 600;
    font-size: var(--font-size-base);
}

.form-group input,
.form-group select,
.form-group textarea {
    width: 100%;
    padding: var(--space-4) var(--space-4);
    border: 2px solid var(--border-color);
    border-radius: var(--radius-lg);
    font-size: var(--font-size-base);
    transition: all var(--transition-normal);
    font-family: inherit;
    background: var(--surface-primary);
    color: var(--text-primary);
}

.form-group input::placeholder,
.form-group textarea::placeholder {
    color: var(--text-secondary);
    opacity: 0.7;
}

.form-group input:focus,
.form-group select:focus,
.form-group textarea:focus {
    outline: none;
    border-color: var(--primary-500);
    background: var(--surface-primary);
    box-shadow: 0 0 0 3px rgba(99, 102, 241, 0.1);
    transform: translateY(-1px);
    color: var(--text-primary);
}

.form-group input:hover,
.form-group select:hover,
.form-group textarea:hover {
    border-color: var(--primary-400);
    background: var(--surface-primary);
    color: var(--text-primary);
}

.form-group textarea {
    resize: vertical;
    min-height: 140px;
    line-height: 1.6;
}

.form-group select {
    cursor: pointer;
    background-image: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><polyline points="6,9 12,15 18,9"></polyline></svg>');
    background-repeat: no-repeat;
    background-position: right var(--space-4) center;
    background-size: 16px;
    appearance: none;
    padding-right: var(--space-12);
}

.form-message {
    margin-top: var(--space-4);
    padding: var(--space-4);
    border-radius: var(--radius-lg);
    text-align: center;
    display: none;
    font-weight: 500;
}

.form-message.success {
    background: var(--success-bg);
    color: var(--success-text);
    border: 1px solid var(--success-border);
}

.form-message.error {
    background: var(--error-bg);
    color: var(--error-text);
    border: 1px solid var(--error-border);
}

.form-submit-btn {
    width: 100%;
    margin-top: var(--space-4);
}

/* ===== ENHANCED FOOTER ===== */
.footer {
    background: var(--surface-secondary);
    color: var(--text-primary);
    padding: var(--space-20) 0 var(--space-8);
    position: relative;
    border-top: 1px solid var(--border-color);
}

.footer::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="footer-pattern" width="40" height="40" patternUnits="userSpaceOnUse"><circle cx="20" cy="20" r="1" fill="rgba(255,255,255,0.05)"/></pattern></defs><rect width="100" height="100" fill="url(%23footer-pattern)"/></svg>');
    opacity: 0.3;
}

.footer-content {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
    gap: var(--space-12);
    margin-bottom: var(--space-12);
    position: relative;
    z-index: 1;
}

.footer-section h3,
.footer-section h4 {
    color: var(--text-primary);
    margin-bottom: var(--space-6);
    font-size: var(--font-size-xl);
}

.footer-section h3 {
    background: linear-gradient(135deg, var(--primary-400) 0%, var(--secondary-400) 100%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    font-size: var(--font-size-2xl);
    font-weight: 800;
}

.footer-section p {
    color: var(--text-secondary);
    margin-bottom: var(--space-3);
    line-height: 1.7;
}

.footer-section ul {
    list-style: none;
}

.footer-section ul li {
    margin-bottom: var(--space-3);
}

.footer-section ul li a {
    color: var(--text-secondary);
    text-decoration: none;
    transition: all var(--transition-normal);
    display: inline-block;
    position: relative;
}

.footer-section ul li a::after {
    content: '';
    position: absolute;
    bottom: -2px;
    left: 0;
    width: 0;
    height: 2px;
    background: linear-gradient(135deg, var(--primary-400) 0%, var(--secondary-400) 100%);
    transition: width var(--transition-normal);
}

.footer-section ul li a:hover {
    color: var(--primary-400);
    transform: translateX(4px);
}

.footer-section ul li a:hover::after {
    width: 100%;
}

.footer-bottom {
    border-top: 1px solid var(--border-color);
    padding-top: var(--space-8);
    text-align: center;
    position: relative;
    z-index: 1;
}

.footer-bottom p {
    color: var(--text-secondary);
    margin: 0;
    font-size: var(--font-size-base);
}

/* ===== ENHANCED SCROLL TO TOP BUTTON ===== */
.scroll-to-top {
    position: fixed;
    bottom: var(--space-6);
    right: var(--space-6);
    background: linear-gradient(135deg, var(--primary-600) 0%, var(--secondary-600) 100%);
    color: var(--text-on-primary);
    border: none;
    border-radius: 50%;
    width: 60px;
    height: 60px;
    font-size: var(--font-size-xl);
    cursor: pointer;
    opacity: 1;
    visibility: visible;
    transition: all var(--transition-normal);
    z-index: 1000;
    box-shadow: var(--shadow-xl);
    display: flex;
    align-items: center;
    justify-content: center;
}

.scroll-to-top.visible {
    opacity: 1;
    visibility: visible;
}

.scroll-to-top:hover {
    background: linear-gradient(135deg, var(--primary-700) 0%, var(--secondary-700) 100%);
    transform: translateY(-4px) scale(1.1);
    box-shadow: var(--shadow-xl);
}

.scroll-to-top:active {
    transform: translateY(-2px) scale(1.05);
}

/* ===== ENHANCED RESPONSIVE DESIGN ===== */

/* Large tablets and small desktops */
@media screen and (max-width: 1024px) {
    .container {
        padding: 0 var(--space-5);
    }

    .hero-container {
        gap: var(--space-12);
    }

    .about-content,
    .value-content,
    .contact-content {
        gap: var(--space-12);
    }

    .cta-box {
        position: static;
        margin-top: var(--space-8);
    }
}

/* Tablets */
@media screen and (max-width: 768px) {
    :root {
        --font-size-6xl: 2.5rem;
        --font-size-5xl: 2rem;
        --font-size-4xl: 1.75rem;
        --font-size-3xl: 1.5rem;
    }

    .hamburger {
        display: flex;
    }

    .nav-menu {
        position: fixed;
        left: -100%;
        top: 80px;
        flex-direction: column;
        background: rgba(255, 255, 255, 0.98);
        backdrop-filter: blur(20px);
        width: 100%;
        text-align: center;
        transition: left var(--transition-normal);
        box-shadow: var(--shadow-xl);
        padding: var(--space-8) 0;
        border-top: 1px solid var(--gray-200);
    }

    .nav-menu.active {
        left: 0;
    }

    .nav-menu li {
        margin: var(--space-4) 0;
    }

    .nav-link {
        font-size: var(--font-size-lg);
        padding: var(--space-3) var(--space-6);
        border-radius: var(--radius-lg);
        transition: all var(--transition-normal);
    }

    .nav-link:hover {
        background: var(--primary-50);
    }

    .hamburger.active .bar:nth-child(2) {
        opacity: 0;
    }

    .hamburger.active .bar:nth-child(1) {
        transform: translateY(7px) rotate(45deg);
    }

    .hamburger.active .bar:nth-child(3) {
        transform: translateY(-7px) rotate(-45deg);
    }

    .hero {
        padding: calc(var(--space-20) + 60px) 0 var(--space-20);
    }

    .hero-container {
        grid-template-columns: 1fr;
        text-align: center;
        gap: var(--space-12);
    }

    .hero-content h1 {
        font-size: var(--font-size-2xl);
    }

    .hero-buttons {
        justify-content: center;
    }

    .hero-stats {
        justify-content: center;
    }

    .hero-logo {
        width: 120px;
        height: 120px;
        padding: var(--space-5);
    }

    .hero-logo-image {
        width: 60px;
        height: 60px;
    }

    .about-content,
    .value-content,
    .contact-content {
        grid-template-columns: 1fr;
        gap: var(--space-8);
    }

    .about-stats {
        flex-direction: row;
        gap: var(--space-4);
    }

    .timeline::before {
        left: var(--space-5);
    }

    .timeline-item {
        flex-direction: column;
        padding-left: var(--space-16);
    }

    .timeline-year {
        left: var(--space-5);
        transform: translateX(-50%);
    }

    .timeline-content {
        width: 100%;
        margin-left: 0;
        margin-right: 0;
    }

    .timeline-content::before {
        display: none;
    }

    .testimonials-grid {
        grid-template-columns: 1fr;
    }

    .testimonial-results {
        justify-content: space-around;
        gap: var(--space-4);
    }

    .services-grid {
        grid-template-columns: 1fr;
        gap: var(--space-6);
    }

    .benefit {
        flex-direction: column;
        text-align: center;
        gap: var(--space-4);
    }

    .benefit-icon {
        margin: 0 auto;
    }
}

/* Mobile phones */
@media screen and (max-width: 480px) {
    :root {
        --font-size-6xl: 2rem;
        --font-size-5xl: 1.75rem;
        --font-size-4xl: 1.5rem;
        --font-size-3xl: 1.25rem;
        --space-24: 4rem;
        --space-20: 3rem;
        --space-16: 2.5rem;
        --space-12: 2rem;
        --space-10: 1.5rem;
        --space-8: 1.25rem;
    }

    .container {
        padding: 0 var(--space-4);
    }

    section {
        padding: var(--space-16) 0;
    }

    .hero {
        padding: calc(var(--space-16) + 60px) 0 var(--space-16);
        min-height: 80vh;
    }

    .hero-content h1 {
        font-size: var(--font-size-xl);
        line-height: 1.2;
    }

    .hero-content .hero-subtitle {
        font-size: var(--font-size-lg);
    }

    .hero-buttons {
        flex-direction: column;
        align-items: center;
        gap: var(--space-3);
    }

    .btn {
        width: 100%;
        max-width: 280px;
        padding: var(--space-4) var(--space-6);
    }

    .hero-stats {
        flex-direction: column;
        gap: var(--space-4);
        text-align: center;
    }

    .hero-logo {
        width: 100px;
        height: 100px;
        padding: var(--space-4);
    }

    .hero-logo-image {
        width: 50px;
        height: 50px;
    }

    .about-stats {
        flex-direction: column;
        gap: var(--space-4);
    }

    .services-grid {
        gap: var(--space-4);
    }

    .service-card {
        padding: var(--space-6);
    }

    .timeline {
        margin: 0 var(--space-4);
    }

    .timeline::before {
        left: var(--space-4);
    }

    .timeline-item {
        padding-left: var(--space-12);
        margin-bottom: var(--space-8);
    }

    .timeline-year {
        left: var(--space-4);
        padding: var(--space-3) var(--space-4);
        font-size: var(--font-size-base);
        min-width: 80px;
    }

    .timeline-content {
        padding: var(--space-6);
        margin-top: var(--space-8);
    }

    .testimonial-card {
        padding: var(--space-6);
    }

    .testimonial-results {
        flex-direction: column;
        gap: var(--space-4);
        text-align: center;
    }

    .contact-form {
        padding: var(--space-6);
    }

    .contact-info,
    .about-text,
    .value-main {
        padding: var(--space-6);
    }

    .footer-content {
        grid-template-columns: 1fr;
        text-align: center;
        gap: var(--space-8);
    }

    .scroll-to-top {
        bottom: var(--space-5);
        right: var(--space-5);
        width: 50px;
        height: 50px;
        font-size: var(--font-size-lg);
    }
}

/* Extra small devices */
@media screen and (max-width: 320px) {
    .hero-content h1 {
        font-size: var(--font-size-lg);
    }

    .section-header h2 {
        font-size: var(--font-size-3xl);
    }

    .btn {
        max-width: 100%;
    }

    .timeline-year {
        min-width: 70px;
        font-size: var(--font-size-sm);
    }
}

/* Animation classes for JavaScript */
.animate-element {
    opacity: 0;
    transform: translateY(30px);
    transition: all 0.8s cubic-bezier(0.4, 0, 0.2, 1);
}

.animate-element.animate-in {
    opacity: 1;
    transform: translateY(0);
}

.fade-in {
    animation: fadeIn 0.6s ease-out forwards;
}

@keyframes fadeIn {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.slide-in-left {
    animation: slideInLeft 0.6s ease-out forwards;
}

@keyframes slideInLeft {
    from {
        opacity: 0;
        transform: translateX(-30px);
    }
    to {
        opacity: 1;
        transform: translateX(0);
    }
}

.slide-in-right {
    animation: slideInRight 0.6s ease-out forwards;
}

@keyframes slideInRight {
    from {
        opacity: 0;
        transform: translateX(30px);
    }
    to {
        opacity: 1;
        transform: translateX(0);
    }
}
